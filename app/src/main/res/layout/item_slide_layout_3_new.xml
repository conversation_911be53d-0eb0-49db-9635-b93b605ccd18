<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
android:orientation="vertical"
android:layout_width="match_parent"
android:layout_height="match_parent">

<!-- <PERSON><PERSON><PERSON> dung ch<PERSON>h chiếm toàn bộ phần còn lại -->
    <LinearLayout
        android:id="@+id/typeContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">
        <LinearLayout
            android:id="@+id/centerContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <TextView
                android:id="@+id/selectingText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="We are choosing one for you"
                android:textSize="22sp"
                android:textColor="?attr/black" />

            <TextView
                android:id="@+id/animatedDots"
                android:layout_width="50dp"
                android:layout_height="wrap_content"
                android:text=""
                android:textSize="22sp"
                android:textColor="@color/black"
                android:gravity="center_vertical" />
        </LinearLayout>



        <LinearLayout

            android:layout_marginTop="30dp"
            android:id="@+id/choiceButtonsLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_gravity="center_horizontal">

            <TextView
                android:paddingTop="20dp"

                android:layout_marginHorizontal="10dp"
                android:id="@+id/typeItem1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Anime"
                android:textAlignment="textStart"
                android:textColor="?attr/black"
                android:textSize="18sp" />


            <TextView
                android:paddingTop="20dp"
                android:layout_marginHorizontal="10dp"
                android:id="@+id/typeItem2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Weather"
                android:textAlignment="textStart"
                android:textColor="?attr/black"
                android:textSize="18sp" />
            <TextView
                android:layout_marginHorizontal="10dp"
                android:paddingTop="20dp"

                android:id="@+id/typeItem3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Animal"
                android:textAlignment="textStart"
                android:textColor="?attr/black"
                android:textSize="18sp" />

            <TextView
                android:paddingTop="20dp"

                android:layout_marginHorizontal="10dp"
                android:id="@+id/typeItem4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Car"
                android:textAlignment="textStart"
                android:textColor="?attr/black"
                android:textSize="18sp" />
        </LinearLayout>

        <ProgressBar
            android:layout_marginHorizontal="30dp"
            android:id="@+id/progressBar"
            style="@android:style/Widget.DeviceDefault.Light.ProgressBar.Horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:indeterminate="false"
            android:max="100"
            android:progressTint="?attr/colorr"
            android:progress="50" />

    </LinearLayout>



    <LinearLayout
        android:id="@+id/next_page"
        android:layout_width="80dp"
        android:layout_height="40dp"
        android:layout_marginBottom="25dp"
        android:layout_marginEnd="30dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:layout_gravity="end"
        android:visibility="visible"
        android:stateListAnimator="@null"
        android:outlineProvider="background">

        <TextView
            android:id="@+id/swipe_text"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_16sdp"
            android:textStyle="bold"
            android:textSize="@dimen/_14ssp"
            android:layout_marginEnd="@dimen/_3sdp"
            android:textAllCaps="true"
            android:textColor="?attr/black"
            android:text="@string/next" />

        <ImageView
            android:layout_width="@dimen/_12sdp"
            android:layout_height="@dimen/_12sdp"
            android:src="@drawable/ic_strelka"
            android:contentDescription="@string/next"
            android:scaleX="-1" />
    </LinearLayout>

<com.facebook.shimmer.ShimmerFrameLayout
    android:id="@+id/nativeAd"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/grey" />
</LinearLayout>
