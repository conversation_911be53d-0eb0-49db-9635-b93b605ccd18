<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
    android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

    <androidx.media3.ui.PlayerView
        android:background="?attr/white"
        app:resize_mode="fill"
        android:id="@+id/playerView"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        app:shutter_background_color="?attr/white"
        android:alpha="0.4"/>

        <!-- Overlay làm mờ video -->
        <View
            android:id="@+id/blurOverlay"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#80000000"/>


        <LinearLayout
            android:layout_marginBottom="10dp"
            android:layout_gravity="bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        <LinearLayout
            android:layout_marginStart="@dimen/_3sdp"
            android:id="@+id/goToMain"
            android:layout_width="80dp"
            android:layout_height="30dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="horizontal"
            android:outlineProvider="background"
            android:stateListAnimator="@null"
            android:visibility="visible">
            <ImageView
                android:layout_width="@dimen/_10sdp"
                android:layout_height="@dimen/_10sdp"
                android:contentDescription="@string/next"
                android:scaleX="1"
                android:src="@drawable/ic_strelka" />

            <TextView
                android:id="@+id/swipe_text"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_marginStart="3dp"
                android:gravity="center"
                android:text="See More"
                android:textAllCaps="true"
                android:textColor="?attr/black"
                android:textSize="@dimen/_9ssp"
                android:textStyle="bold" />

        </LinearLayout>

            <LinearLayout

                android:layout_gravity="center"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="9dp"
                android:animateLayoutChanges="true"
                android:background="@drawable/white_block"
                android:orientation="vertical">

                <Button
                    android:textSize="@dimen/_15ssp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:textColor="?attr/black"
                    android:id="@+id/accept_animation"
                    android:background="@drawable/grey_block"
                    android:paddingTop="12.5dp"
                    android:paddingBottom="12.5dp"
                    android:longClickable="false"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:text="Accept Aniamation for Charging"
                    android:textAllCaps="false"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    style="@style/Widget.AppCompat.Button.Borderless"/>
            </LinearLayout>
        </LinearLayout>

    </FrameLayout>


    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/nativeAd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/grey" />
</LinearLayout>
