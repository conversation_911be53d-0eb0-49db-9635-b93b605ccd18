package com.tqhit.battery.one.activity.tutorial


import android.content.Intent
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.base.LocaleAwareActivity
import com.tqhit.battery.one.databinding.ActivityTutorialBinding
import com.tqhit.battery.one.utils.OverlayPermissionUtils
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class TutorialActivity : LocaleAwareActivity<ActivityTutorialBinding>() {

    override val binding by lazy { ActivityTutorialBinding.inflate(layoutInflater) }

    private var overlayPermissionLauncher: ActivityResultLauncher<Intent>? = null


    override fun setupData() {
        super.setupData()
        overlayPermissionLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (!android.provider.Settings.canDrawOverlays(this)) {
                Toast.makeText(this, getString(R.string.overlay_permission_denied), Toast.LENGTH_SHORT).show()
                return@registerForActivityResult
            }
            if (OverlayPermissionUtils.isOverlayPermissionGranted(this)) {
                binding.goToSettingButton.text = "Overlays is Enable"
                binding.goToSettingButton.isEnabled = false
//                navigationToMain()
            } else {
                Toast.makeText(this, "Permission not granted", Toast.LENGTH_SHORT).show()
            }
        }


    }

    override fun setupUI() {
        super.setupUI()
        val isGranted = OverlayPermissionUtils.isOverlayPermissionGranted(this)
        binding.goToSettingButton.isEnabled = !isGranted
        if (isGranted)
        {
            binding.goToSettingButton.text = "Overlays Permission is Enable"
        }

    }

    override fun setupListener() {
        super.setupListener()

        binding.goToSettingButton.setOnClickListener{
            overlayPermission()
        }
        binding.includeBackNavigation.btnBackNavigation.setOnClickListener{
            finish()
        }
    }

    private fun overlayPermission() {
            if (!OverlayPermissionUtils.isOverlayPermissionGranted(this)) {
                val intent = OverlayPermissionUtils.createOverlayPermissionIntent(this)
                overlayPermissionLauncher!!.launch(intent)
            }
        }

    override fun onResume() {
        super.onResume()
        if (OverlayPermissionUtils.isOverlayPermissionGranted(this)) {
            navigationToMain()
        }
    }
    private fun navigationToMain(){
        startActivity(Intent(this, MainActivity::class.java))
        finish()
    }


}
