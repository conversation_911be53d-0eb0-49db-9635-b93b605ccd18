package com.tqhit.battery.one.activity.splash

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.lifecycleScope
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.battery.one.BatteryApplication
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.activity.starting.StartingActivity
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.databinding.ActivitySplashBinding
import com.tqhit.battery.one.fragment.onboarding.LanguageSelectionViewModel
import com.tqhit.battery.one.initialization.InitializationProgressManager
import com.tqhit.battery.one.utils.DeviceUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Timer
import java.util.TimerTask
import javax.inject.Inject

@AndroidEntryPoint
class SplashActivity : AdLibBaseActivity<ActivitySplashBinding>() {
    override val binding by lazy { ActivitySplashBinding.inflate(layoutInflater) }

    private val appViewModel: AppViewModel by viewModels()

    @Inject
    lateinit var initializationProgressManager: InitializationProgressManager

    private val mainHandler = Handler(Looper.getMainLooper())
    private var hasNavigated = false
    private var splashStartTime = 0L // Track when splash screen started
    private var hasShownAd = false

    // Timeout safety mechanisms
    private var emergencyTimer: Timer? = null
    private var watchdogTimer: Timer? = null
    private var isShowedStartPageCached: Boolean? = null // Cache to avoid main thread blocking

    @Inject
    lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager

    companion object {
        private const val TAG = "SplashActivity"
        private const val MAX_SPLASH_DURATION_MS = 5000L // 5 seconds maximum
        private const val MIN_SPLASH_DURATION_MS = 1000L // 1 second minimum for UX
        private const val EMERGENCY_TIMEOUT_MS = 6000L // 6 seconds emergency backup
        private const val WATCHDOG_TIMEOUT_MS = 6500L // 6.5 seconds final failsafe
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        splashStartTime = System.currentTimeMillis()

        try {
            // Apply device-specific adjustments
            applyDeviceSpecificAdjustments()

            val splashScreen = installSplashScreen()
            super.onCreate(savedInstanceState)

            // Set the content view to display our custom layout
            if (binding.root.parent != null) {
                (binding.root.parent as? android.view.ViewGroup)?.removeView(binding.root)
            }

            setContentView(binding.root)
            saveLanguage("en")
            // Ensure splash screen transitions properly to our custom layout
            splashScreen.setOnExitAnimationListener { splashScreenView ->
                try {
                    splashScreenView.remove()
                } catch (e: Exception) {
                    Log.e(TAG, "Error in splash screen exit animation", e)
                }
            }



        } catch (e: Exception) {
            Log.e(TAG, "Critical error in onCreate", e)
            // Fallback: try to continue without custom splash screen handling
            try {
                super.onCreate(savedInstanceState)
                setContentView(binding.root)
                Log.d(TAG, "SPLASH_PROGRESS: Fallback content view set successfully")
            } catch (fallbackException: Exception) {
                Log.e(TAG, "CRITICAL - Fallback also failed", fallbackException)
                finish()
                return
            }
        }

        // Setup progress UI and start initialization
        setupProgressUIImmediately()

        // Cache SharedPreferences access to avoid main thread blocking during navigation
        cacheNavigationData()

        // Start initialization process
        startInitializationProcess()

        // Setup timeout mechanisms (primary, emergency, and watchdog)
        setupMaximumTimeout()
        setupEmergencyTimeout()
        setupWatchdogTimer()

        // Single timing log at end to minimize overhead
        val totalOnCreateDuration = System.currentTimeMillis() - splashStartTime
        Log.d(TAG, "STARTUP_TIMING: SplashActivity.onCreate() completed in ${totalOnCreateDuration}ms")
    }
    
    /**
     * Apply device-specific adjustments to ensure compatibility
     */
    private fun applyDeviceSpecificAdjustments() {
        try {
            DeviceUtils.applyDeviceSpecificAdjustments(this)

            // Check for Xiaomi device
            if (DeviceUtils.isXiaomiDevice()) {
                // Use appropriate hardware acceleration settings for MIUI
                try {
                    window?.setFlags(
                        android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                        android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                    )

                    // Also set specific layer types for better compatibility
                    binding.root.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
                } catch (e: Exception) {
                    Log.e(TAG, "Error setting hardware acceleration flags", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error applying device adjustments", e)
        }
    }

    override fun setupData() {
        // Override to prevent immediate navigation
        // Navigation will be handled by initialization completion
        // Don't call super.setupData() to prevent immediate navigation
    }

    /**
     * Setup progress UI immediately to show loading state
     */
    private fun setupProgressUIImmediately() {
        // Make progress container visible immediately
        binding.progressContainer.visibility = View.VISIBLE

        // Set initial progress state immediately with optimized resource access
        binding.progressBarInitialization.progress = 0

        // Use cached strings to avoid resource lookup delays
        val initializingText = try {
            getString(R.string.splash_starting_initialization)
        } catch (e: Exception) {
            "Starting initialization..."
        }

        val zeroPercentText = try {
            getString(R.string.progress_zero_percent)
        } catch (e: Exception) {
            "0%"
        }

        binding.tvInitializationStatus.text = initializingText
        binding.tvProgressPercentage.text = zeroPercentText

        // Now setup the progress tracking observer
        setupProgressTracking()
    }

    /**
     * Handles the language confirmation.
     */
    private fun saveLanguage(languageCode: String) {
        // Save language using ViewModel
        appViewModel.setLanguage(languageCode)

        // Apply the new language
        appViewModel.setLocale(this, languageCode)
    }


    /**
     * Setup progress tracking UI and observe initialization state
     */
    private fun setupProgressTracking() {
        // Progress container is already visible from setupProgressUIImmediately()

        // Observe initialization progress with optimized coroutine scope
        lifecycleScope.launch(Dispatchers.Main.immediate) {
            initializationProgressManager.initializationProgress.collectLatest { state ->
                updateProgressUI(state)
                // Check if initialization is complete or has critical errors
//                if ((state.isComplete || state.hasError) && !hasNavigated) {
//                    navigateWithMinimumDelay()
//                }
            }
        }
    }

    /**
     * Start the initialization process
     */
    private fun startInitializationProcess() {
        initializationProgressManager.startInitialization()
    }

    /**
     * Setup maximum timeout to ensure splash doesn't stay too long
     */
    private fun setupMaximumTimeout() {
        mainHandler.postDelayed({
            if (!hasNavigated) {
                navigateWithMinimumDelay()
            }
        }, MAX_SPLASH_DURATION_MS)
    }

    /**
     * Setup emergency timeout as backup to primary timeout mechanism
     * This provides an additional safety net if the primary timeout fails
     */
    private fun setupEmergencyTimeout() {
        emergencyTimer = Timer("SplashEmergencyTimeout", true)
        emergencyTimer?.schedule(object : TimerTask() {
            override fun run() {
                if (!hasNavigated) {
                    Log.w(TAG, "Emergency timeout reached, forcing navigation")
                    runOnUiThread {
                        forceNavigationEmergency()
                    }
                }
            }
        }, EMERGENCY_TIMEOUT_MS)
    }

    /**
     * Setup watchdog timer as final failsafe mechanism
     * This operates independently of Handler-based timeouts
     */
    private fun setupWatchdogTimer() {
        watchdogTimer = Timer("SplashWatchdogTimer", true)
        watchdogTimer?.schedule(object : TimerTask() {
            override fun run() {
                if (!hasNavigated) {
                    Log.e(TAG, "Watchdog timeout reached - critical timeout violation")
                    runOnUiThread {
                        forceNavigationWatchdog()
                    }
                }
            }
        }, WATCHDOG_TIMEOUT_MS)
    }

    /**
     * Cache navigation data to avoid main thread blocking during navigation
     * This prevents SharedPreferences access from blocking the timeout mechanism
     */
    private fun cacheNavigationData() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Cache the onboarding status to avoid main thread SharedPreferences access
                val showedStartPage = appViewModel.isShowedStartPage()

                withContext(Dispatchers.Main) {
                    isShowedStartPageCached = showedStartPage
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error caching navigation data", e)
                withContext(Dispatchers.Main) {
                    // Fallback to default behavior if caching fails
                    isShowedStartPageCached = false
                }
            }
        }
    }

    /**
     * Update progress UI based on initialization state
     */
    private fun updateProgressUI(state: com.tqhit.battery.one.initialization.InitializationState) {
        // Optimize UI updates by avoiding unnecessary runOnUiThread calls
        // since this is already called from Main dispatcher in setupProgressTracking
        try {
            // Batch UI updates to reduce layout passes
            binding.progressBarInitialization.progress = state.progress
            binding.tvInitializationStatus.text = state.statusMessage
            binding.tvProgressPercentage.text = "${state.progress}%"

            // Optimize color changes by caching color values
            val textColor = when {
                state.hasError -> getColor(android.R.color.holo_red_dark)
                state.isComplete -> getColor(android.R.color.holo_green_dark)
                else -> getColor(android.R.color.black)
            }
            binding.tvInitializationStatus.setTextColor(textColor)

            // Minimal logging - only major milestones
            if (state.progress == 100 || state.hasError) {
                Log.d(TAG, "STARTUP_TIMING: Progress ${state.progress}% - ${state.statusMessage}")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error updating progress UI", e)
        }
    }

    /**
     * Navigate with minimum delay enforcement to ensure splash screen is visible
     */
    private fun navigateWithMinimumDelay() {
        if (hasNavigated) {
            return
        }

        val currentTime = System.currentTimeMillis()
        val elapsedTime = currentTime - splashStartTime
        val remainingTime = MIN_SPLASH_DURATION_MS - elapsedTime

        if (remainingTime > 0) {
            // Check if minimum delay would cause timeout violation
            if (elapsedTime + remainingTime > MAX_SPLASH_DURATION_MS) {
                navigateToNextActivity()
            } else {
                mainHandler.postDelayed({
                    navigateToNextActivity()
                }, remainingTime)
            }
        } else {
            navigateToNextActivity()
        }
    }

    /**
     * Show IV
     * loop 0,5s if loading
     */
//    private fun showIV() {
//        if (hasNavigated || hasShownAd) return
//        if (applovinInterstitialAdManager.isReady()) {
//            hasShownAd = true
//            applovinInterstitialAdManager.showInterstitialAd(
//                "default_iv",
//                this@SplashActivity
//            )
//            Log.d("ApplovinInterstitialAdManager", "Ad is ready, showing")
//            navigateToNextActivity()
//        } else {
//            Log.d("ApplovinInterstitialAdManager", "Ad not ready")
//            mainHandler.postDelayed({ showIV() }, 500)
//        }
//
//    }

    /**
     * Navigate to the next activity based on user onboarding status
     * Uses cached data to avoid main thread blocking
     */
    private fun navigateToNextActivity() {
        if (hasNavigated) {
            return
        }
        performNavigationWithAdLoading()
    }

    /**
     * Emergency navigation when primary timeout fails
     */
    private fun forceNavigationEmergency() {
        if (hasNavigated) {
            return
        }
        Log.w(TAG, "Emergency navigation triggered - primary timeout failed")
        performNavigationWithAdLoading()
    }

    /**
     * Watchdog navigation when all other mechanisms fail
     */
    private fun forceNavigationWatchdog() {
        if (hasNavigated) {
            return
        }
        Log.e(TAG, "Watchdog navigation triggered - critical timeout violation")
        performNavigationWithAdLoading()
    }

    /**
     * Perform the actual navigation with ad loading logic
     * Centralized method to avoid code duplication and ensure consistent behavior
     */
    private fun performNavigationWithAdLoading() {
        val navigationStartTime = System.currentTimeMillis()
        val splashTotalDuration = navigationStartTime - splashStartTime
        Log.d(TAG, "SPLASH_TO_LANGUAGE: Navigation initiated after ${splashTotalDuration}ms splash duration")

        hasNavigated = true

        // Use cached data to avoid main thread blocking, fallback to direct access if needed
        val showedStartPage = isShowedStartPageCached ?: appViewModel.isShowedStartPage()

        if (showedStartPage) {
            Log.d(TAG, "SPLASH_TO_LANGUAGE: Navigating to MainActivity")
            startActivity(Intent(this, MainActivity::class.java))
        } else {
            Log.d(TAG, "SPLASH_TO_LANGUAGE: Navigating to LanguageSelectionActivity")
//            val intent = Intent(this, LanguageSelectionActivity::class.java)
            val intent = Intent(this, StartingActivity::class.java)
            startActivity(intent)
        }

        // Clean up timers
        cleanupTimeoutMechanisms()

        Log.d(TAG, "SPLASH_TO_LANGUAGE: Navigation completed, splash activity finishing")
        finish()
    }

    /**
     * Clean up timeout mechanisms to prevent memory leaks
     */
    private fun cleanupTimeoutMechanisms() {
        try {
            emergencyTimer?.cancel()
            emergencyTimer = null

            watchdogTimer?.cancel()
            watchdogTimer = null
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up timeout mechanisms", e)
        }
    }

    // Activity Lifecycle Protection Methods

    override fun onPause() {
        super.onPause()

        // Check if we've exceeded the maximum timeout while paused
        val currentTime = System.currentTimeMillis()
        val elapsedTime = currentTime - splashStartTime

        if (!hasNavigated && elapsedTime > MAX_SPLASH_DURATION_MS) {
            navigateWithMinimumDelay()
        }
    }

    override fun onStop() {
        super.onStop()

        // Additional check when activity is stopped
        val currentTime = System.currentTimeMillis()
        val elapsedTime = currentTime - splashStartTime

        if (!hasNavigated && elapsedTime > MAX_SPLASH_DURATION_MS + 1000L) {
            forceNavigationEmergency()
        }
    }

    override fun onDestroy() {
        // Clean up timeout mechanisms
        cleanupTimeoutMechanisms()

        super.onDestroy()
    }
}
