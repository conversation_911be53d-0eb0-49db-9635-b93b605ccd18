<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:background="?attr/grey"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <include
        android:id="@+id/include_back_navigation"
        layout="@layout/layout_back_navigation" />

    <TextView

        android:layout_marginTop="20dp"
        android:paddingHorizontal="16dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="You must turn “Display over other Apps” on to use Animation Charging"
        android:textColor="?attr/black"
        android:textSize="16dp"
        android:textStyle="bold" />
<ScrollView
    android:background="?attr/grey"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_weight="1"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">



        <!-- Step 1 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1. Select “Installed Apps” or “Downloaded App” in Setting"
            android:textColor="?attr/black"
            android:textStyle="bold" />

        <ImageView
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:adjustViewBounds="true"
            android:background="@drawable/step_1" />

        <!-- Step 2 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="2. Select “Battery Charging Animation 3D”"
            android:textColor="?attr/black"
            android:textStyle="bold" />

        <ImageView
            android:layout_gravity="center"

            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:adjustViewBounds="true"
            android:background="@drawable/step_2" />

        <!-- Step 3 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="3. Select “Display over other Apps”"
            android:textColor="?attr/black"
            android:textStyle="bold" />

        <ImageView
            android:layout_gravity="center"

            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:adjustViewBounds="true"
            android:background="@drawable/step_3" />

        <!-- Step 4 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="4. Turn On"
            android:textColor="?attr/black"
            android:textStyle="bold" />

        <ImageView
            android:layout_gravity="center"

            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:adjustViewBounds="true"
            android:background="@drawable/step_4" />

    </LinearLayout>
</ScrollView>
    <!-- Button -->
    <LinearLayout
        android:layout_margin="@dimen/_16sdp"
        android:layout_gravity="center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:background="@drawable/white_block"
        android:orientation="vertical">

        <Button
            android:textSize="@dimen/_14ssp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:textColor="?attr/black"
            android:id="@+id/goToSettingButton"
            android:background="@drawable/grey_block"
            android:paddingTop="12.5dp"
            android:paddingBottom="12.5dp"
            android:longClickable="false"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:text="Go to Overlay Permission Setting"
            android:textAllCaps="false"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            style="@style/Widget.AppCompat.Button.Borderless"/>
    </LinearLayout>
</LinearLayout>
