package com.tqhit.battery.one.activity.starting

import android.content.Intent
import android.net.Uri
import android.os.Handler
import android.util.Log
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.OptIn
import androidx.core.net.toUri
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.viewbinding.ViewBinding
import com.google.gson.Gson
import com.ironsource.nu
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.activity.tutorial.TutorialActivity
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
import com.tqhit.battery.one.base.LocaleAwareActivity
import com.tqhit.battery.one.databinding.ActivityStartingBinding
import com.tqhit.battery.one.databinding.ItemSlideLayout1NewBinding
import com.tqhit.battery.one.databinding.ItemSlideLayout2Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout3NewBinding
import com.tqhit.battery.one.databinding.ItemSlideLayout4NewBinding
import com.tqhit.battery.one.databinding.ItemSlideLayout5NewBinding
import com.tqhit.battery.one.fragment.main.animation.data.AnimationCategory
import com.tqhit.battery.one.utils.BatteryLogger
import com.tqhit.battery.one.utils.OverlayPermissionUtils
import com.tqhit.battery.one.utils.VideoUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

@AndroidEntryPoint
class StartingActivity : LocaleAwareActivity<ActivityStartingBinding>() {
    override val binding by lazy { ActivityStartingBinding.inflate(layoutInflater) }

    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager

    @Inject lateinit var applovinNativeAdManager: ApplovinNativeAdManager
    @Inject
    lateinit var remoteConfigHelper: FirebaseRemoteConfigHelper

    private val appViewModel: AppViewModel by viewModels()
    private val batteryViewModel: BatteryViewModel by viewModels()

    private var player: ExoPlayer? = null

    private var selecting = false
    private var highlightIndex = 0

    private val animationViewModel: AnimationViewModel by viewModels()
    private var mediaUrl = ""

    @Inject
    lateinit var videoUtils: com.tqhit.battery.one.utils.VideoUtils


    private var currentDotRunnable: Runnable? = null
    private var currentProgressRunnable: Runnable? = null
    private var currentButtonRunnable: Runnable? = null

    private var handlerDots: Handler? = null
    private var handlerProgress: Handler? = null
    private var handlerButtons: Handler? = null

    var isVideoRemoteReady = false

    companion object {
        private const val TAG = "StartingActivity"
    }

    /**
     * Logs comprehensive language and locale debugging information
     */
    private fun logLanguageDebugInfo() {
        try {
            val currentLocale = resources.configuration.locales[0]
            val savedLanguage = if (::appRepository.isInitialized) appRepository.getLanguage() else "N/A"

            Log.d(TAG, "=== STARTING ACTIVITY LANGUAGE DEBUG ===")
            Log.d(TAG, "Current locale: $currentLocale")
            Log.d(TAG, "Saved language from repository: $savedLanguage")
            Log.d(TAG, "Default locale: ${java.util.Locale.getDefault()}")
            Log.d(TAG, "Activity context locale: ${this.resources.configuration.locales[0]}")
            Log.d(TAG, "Application context locale: ${applicationContext.resources.configuration.locales[0]}")

            // Test comprehensive string resource loading
            logStringResources()

            Log.d(TAG, "=== END STARTING ACTIVITY LANGUAGE DEBUG ===")
        } catch (e: Exception) {
            Log.e(TAG, "Error in language debug logging", e)
        }
    }

    /**
     * Logs all key string resources used in onboarding slides
     */
    private fun logStringResources() {
        try {
            Log.d(TAG, "--- STRING RESOURCES TEST ---")

            // Basic app strings
            Log.d(TAG, "app_name: '${getString(R.string.app_name)}'")

            // Onboarding specific strings
            try {
                Log.d(TAG, "privacy_policy_starting: '${getString(R.string.privacy_policy_starting)}'")
            } catch (e: Exception) {
                Log.w(TAG, "Failed to load privacy_policy_starting", e)
            }

            try {
                Log.d(TAG, "confirmed: '${getString(R.string.confirmed)}'")
            } catch (e: Exception) {
                Log.w(TAG, "Failed to load confirmed", e)
            }

            // Test other common onboarding strings that exist
            try {
                val nextText = getString(R.string.next)
                Log.d(TAG, "next: '$nextText'")
            } catch (e: Exception) {
                Log.w(TAG, "Failed to load 'next' string", e)
            }

            Log.d(TAG, "--- END STRING RESOURCES TEST ---")
        } catch (e: Exception) {
            Log.e(TAG, "Error in string resources logging", e)
        }
    }

    /**
     * Logs the content of a specific slide to track localization
     */
    private fun logSlideContent(slideName: String, binding: ViewBinding) {
        try {
            Log.d(TAG, "--- $slideName CONTENT ---")

            when (binding) {
                is ItemSlideLayout1NewBinding -> {
                }
                is ItemSlideLayout2Binding -> {
                    Log.d(TAG, "Slide 2 - Privacy button text: '${binding.privacyPolicyButton.text}'")
                    Log.d(TAG, "Slide 2 - Confirm button text: '${binding.confirmAndContinueButton.text}'")
                    Log.d(TAG, "Slide 2 - Under text: '${binding.underText.text}'")
                }
                is ItemSlideLayout3NewBinding -> {
                    // Log slide 3 content
                    Log.d(TAG, "Slide 3 - Content logged")
                }
                is ItemSlideLayout4NewBinding -> {
                    // Log slide 4 content
                    Log.d(TAG, "Slide 4 - Content logged")
                }
                is ItemSlideLayout5NewBinding -> {
                    // Log other slide 5 content
                    Log.d(TAG, "Slide 5 - Content logged")
                }
//                is ItemSlideLayout6Binding -> {
//                    // Log slide 6 content
//                    Log.d(TAG, "Slide 6 - Content logged")
//                }
//                is ItemSlideLayout7Binding -> {
//                    Log.d(TAG, "Slide 7 - Change capacity text: '${binding.changeCapacity.text}'")
//                    // Note: getStarted property may not exist, check the actual binding
//                    Log.d(TAG, "Slide 7 - Content logged")
//                }
            }

            Log.d(TAG, "--- END $slideName CONTENT ---")
        } catch (e: Exception) {
            Log.e(TAG, "Error logging slide content for $slideName", e)
        }
    }

    private val layouts =
            listOf(
                    R.layout.item_slide_layout_1_new,
                    R.layout.item_slide_layout_2,
                    R.layout.item_slide_layout_3_new,
                    R.layout.item_slide_layout_4_new,
                R.layout.item_slide_layout_5_new,
//                    R.layout.item_slide_layout_6,
//                    R.layout.item_slide_layout_7,
            )

    private val views = arrayListOf<ViewBinding>()

    private val startingViewAdapter by lazy {
        StartingViewAdapter(views.filter { view -> view !is ItemSlideLayout2Binding }
            .toCollection(ArrayList()), applovinNativeAdManager, remoteConfigHelper, this)
    }

//    private val permissionLauncher =
//            registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
//                val layout5Binding = views[4] as ItemSlideLayout5Binding
//                if (isGranted) {
//                    layout5Binding.switchInfo.isChecked = true
//                    layout5Binding.switchInfo.isEnabled = false
//                    appViewModel.setChargeAlarmEnabled(true)
//                    PermissionUtils.resetNotificationPermissionDeniedCount()
//                } else {
//                    layout5Binding.switchInfo.isChecked = false
//                    layout5Binding.switchInfo.isEnabled = true
//                    appViewModel.setChargeAlarmEnabled(false)
//                    if (!PermissionUtils.shouldShowRequestPermissionRationale(this)) {
//                        PermissionUtils.incrementNotificationPermissionDeniedCount()
//                    }
//                }
//            }


    private val permissionLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { isGranted ->
            if (OverlayPermissionUtils.isOverlayPermissionGranted(this)) {
                navigationToMain()
            } else {
                Log.d(TAG, "Overlay permission denied")
            }
        }

    private fun navigationToMain(){
        applovinInterstitialAdManager.showInterstitialAd(
            "default_iv",
            this@StartingActivity
        ) {
//            appViewModel.setShowedStartPage(true)
            startActivity(Intent(this, MainActivity::class.java))
            finish()
        }
    }

    override fun setupData() {
        super.setupData()


        // Enhanced logging for language debugging
        logLanguageDebugInfo()

        // Check if we need to create a locale-aware LayoutInflater
        val savedLanguage = if (::appRepository.isInitialized) appRepository.getLanguage() else ""
        Log.d(TAG, "=== LAYOUT INFLATER SETUP ===")
        Log.d(TAG, "Saved language: '$savedLanguage'")
        Log.d(TAG, "Default LayoutInflater context locale: ${layoutInflater.context.resources.configuration.locales[0]}")

        // Use the default LayoutInflater since the activity context is already locale-aware
        val localeAwareInflater = layoutInflater
        Log.d(TAG, "Using activity's default LayoutInflater with context locale: ${layoutInflater.context.resources.configuration.locales[0]}")

        Log.d(TAG, "Final LayoutInflater context locale: ${localeAwareInflater.context.resources.configuration.locales[0]}")
        Log.d(TAG, "=== END LAYOUT INFLATER SETUP ===")

        for ((index, layout) in layouts.withIndex()) {
            Log.d(TAG, "=== CREATING SLIDE ${index + 1} ===")
            Log.d(TAG, "Layout resource: $layout")
            Log.d(TAG, "Using LayoutInflater context locale: ${localeAwareInflater.context.resources.configuration.locales[0]}")

            val binding = when (layout) {
                R.layout.item_slide_layout_1_new -> {
                    Log.d(TAG, "Inflating ItemSlideLayout1NewBinding")
                    val slideBinding = ItemSlideLayout1NewBinding.inflate(localeAwareInflater)
                    logSlideContent("Slide 1", slideBinding)
                    slideBinding
                }
                R.layout.item_slide_layout_2 -> {
                    Log.d(TAG, "Inflating ItemSlideLayout2Binding")
                    val slideBinding = ItemSlideLayout2Binding.inflate(localeAwareInflater)
                    logSlideContent("Slide 2", slideBinding)
                    slideBinding
                }
                R.layout.item_slide_layout_3_new -> {
                    Log.d(TAG, "Inflating ItemSlideLayout3NewBinding")
                    val slideBinding = ItemSlideLayout3NewBinding.inflate(localeAwareInflater)
                    logSlideContent("Slide 3", slideBinding)
                    slideBinding
                }
                R.layout.item_slide_layout_4_new -> {
                    Log.d(TAG, "Inflating ItemSlideLayout4Binding")
                    val slideBinding = ItemSlideLayout4NewBinding.inflate(localeAwareInflater)
                    logSlideContent("Slide 4", slideBinding)
                    slideBinding
                }
                R.layout.item_slide_layout_5_new -> {
                    Log.d(TAG, "Inflating ItemSlideLayout5Binding")
                    val slideBinding = ItemSlideLayout5NewBinding.inflate(localeAwareInflater)
                    logSlideContent("Slide 5", slideBinding)
                    slideBinding
                }
//                R.layout.item_slide_layout_6 -> {
//                    Log.d(TAG, "Inflating ItemSlideLayout6Binding")
//                    val slideBinding = ItemSlideLayout6Binding.inflate(localeAwareInflater)
//                    logSlideContent("Slide 6", slideBinding)
//                    slideBinding
//                }
//                R.layout.item_slide_layout_7 -> {
//                    Log.d(TAG, "Inflating ItemSlideLayout7Binding")
//                    val slideBinding = ItemSlideLayout7Binding.inflate(localeAwareInflater)
//                    logSlideContent("Slide 7", slideBinding)
//                    slideBinding
//                }
                else -> break
            }

            Log.d(TAG, "Slide ${index + 1} root view context locale: ${binding.root.context.resources.configuration.locales[0]}")
            views.add(binding)
            Log.d(TAG, "=== SLIDE ${index + 1} CREATION COMPLETE ===")
        }

        Log.d(TAG, "=== SETTING UP SLIDE CONTENT WITH STRING RESOURCES ===")

        val layout2Binding = views[1] as ItemSlideLayout2Binding
        val privacyPolicyAccepted = appViewModel.isPrivacyPolicyAccepted()

        Log.d(TAG, "Privacy policy accepted: $privacyPolicyAccepted")

        layout2Binding.confirmAndContinueButton.visibility =
                privacyPolicyAccepted.let { if (it) View.GONE else View.VISIBLE }

        val underTextValue = privacyPolicyAccepted.let {
            if (it) {
                val confirmedText = getString(R.string.confirmed)
                Log.d(TAG, "Loading 'confirmed' string: '$confirmedText'")
                confirmedText
            } else {
                val privacyText = getString(R.string.privacy_policy_starting)
                Log.d(TAG, "Loading 'privacy_policy_starting' string: '$privacyText'")
                privacyText
            }
        }

        layout2Binding.underText.text = underTextValue
        Log.d(TAG, "Final underText value: '${layout2Binding.underText.text}'")

//        val layout5Binding = views[4] as ItemSlideLayout5Binding
//        layout5Binding.switchInfo.isChecked = PermissionUtils.isNotificationPermissionGranted(this)
//        appViewModel.setChargeAlarmEnabled(layout5Binding.switchInfo.isChecked)
//        layout5Binding.switchInfo.isEnabled = !layout5Binding.switchInfo.isChecked
//        layout5Binding.textPercent.text = buildString {
//            append(appViewModel.getChargeAlarmPercent())
//            append("%")
//        }
//        layout5Binding.circularbar.progress = appViewModel.getChargeAlarmPercent().toFloat()

        // Fill in device information in layout 7
//        val layout7Binding = views[6] as ItemSlideLayout7Binding
//        // Set device name
//        val deviceName =
//                if (Build.MODEL.lowercase().startsWith(Build.MANUFACTURER.lowercase())) {
//                    Build.MODEL
//                } else {
//                    "${Build.MANUFACTURER} ${Build.MODEL}"
//                }
//        layout7Binding.deviceName.text = deviceName

        // Observe battery capacity changes regardless of charging state
//        lifecycleScope.launch {
//            repeatOnLifecycle(Lifecycle.State.STARTED) {
//                batteryViewModel.batteryCapacity.collect { capacity ->
//                    layout7Binding.capacity.text = buildString {
//                        append(capacity)
//                        append(getString(R.string.ma))
//                    }
//                }
//            }
//        }

        // Observe charging state and update UI accordingly
//        lifecycleScope.launch {
//            repeatOnLifecycle(Lifecycle.State.STARTED) {
//                batteryViewModel.isCharging.collect { isCharging ->
//                    val isDischarging = !isCharging
//                    layout7Binding.discharging.text =
//                            if (isDischarging) getString(R.string.yes) else getString(R.string.no)
//
//                    // Show text5 only when discharging
//                    layout7Binding.text5.visibility = if (isDischarging) View.GONE else View.VISIBLE
//
//                    // Update UI based on discharging state
//                    if (isDischarging) {
//                        // Set polarity
//                        layout7Binding.polarity.text = batteryViewModel.getBatteryPolarity()
//                        // Set measurement parameter
//                        layout7Binding.parameter.text = getString(R.string.mA)
//                        // Show button and hide progress when discharging
//                        layout7Binding.nextPage.visibility = View.VISIBLE
//                        // Enable start button when discharging
//                        layout7Binding.nextPage.isEnabled = true
//                    } else {
//                        // When not discharging, hide diagnostics
//                        layout7Binding.polarity.text = getString(R.string.not_identified)
//                        layout7Binding.parameter.text = getString(R.string.not_identified)
//                        // Show progress and hide button when not discharging
//                        layout7Binding.nextPage.visibility = View.GONE
//                        // Disable start button when not discharging
//                        layout7Binding.nextPage.isEnabled = false
//                    }
//                }
//            }
//        }
    }

    override fun setupUI() {
        super.setupUI()

        binding.slidePager.adapter = startingViewAdapter
        binding.springDotsIndicator.attachTo(binding.slidePager)
//        setupNativeAd()


        val layout4Binding = views[3] as ItemSlideLayout4NewBinding
        getRandomAnimationVideoUrl()
        setupPlayer(layout4Binding, mediaUrl)
    }




    private fun startDotAnimation(
        textView: TextView,
        dotDelay: Long = 300L
    ) {
        currentDotRunnable?.let { handlerDots?.removeCallbacks(it) }

        handlerDots = Handler(mainLooper)
        val localHandler = handlerDots!!

        var dotCount = 1
        val dotRunnable = object : Runnable {
            override fun run() {
                textView.text = ".".repeat(dotCount)
                dotCount = if (dotCount >= 6) 1 else dotCount + 1
                localHandler.postDelayed(this, dotDelay)
            }
        }

        currentDotRunnable = dotRunnable
        localHandler.post(dotRunnable)
    }


    private fun startProgressAnimation(
        progressBar: ProgressBar,
        animatedDots: TextView,
        textView: TextView,
        finalText: String,
        progressDelay: Long = 10L
    ) {
        currentProgressRunnable?.let { handlerProgress?.removeCallbacks(it) }

        handlerProgress = Handler(mainLooper)
        val localHandler = handlerProgress!!

        var progress = 0
        val progressRunnable = object : Runnable {
            override fun run() {
                if (progress <= 100) {
                    progressBar.progress = progress
                    progress += 5
                    localHandler.postDelayed(this, 40L)
                } else {
                    animatedDots.visibility = View.GONE
                    textView.text = finalText
                }
            }
        }

        currentProgressRunnable = progressRunnable
        localHandler.post(progressRunnable)
    }


    fun startItemAnimation() {
        val layout3Binding = views[2] as ItemSlideLayout3NewBinding
        val buttons = listOf(
            layout3Binding.typeItem1,
            layout3Binding.typeItem2,
            layout3Binding.typeItem3,
            layout3Binding.typeItem4
        )
        startBouncingButtonsInOrder(buttons)
    }

    private fun startBouncingButtonsInOrder(buttons: List<View>) {
        currentButtonRunnable?.let { handlerButtons?.removeCallbacks(it) }

        handlerButtons = Handler(mainLooper)
        val localHandler = handlerButtons!!

        val animation = AnimationUtils.loadAnimation(this, R.anim.bounce_up_down)
        var currentIndex = 0

        val runnable = object : Runnable {
            override fun run() {
                if (buttons.isNotEmpty()) {
                    buttons[currentIndex].startAnimation(animation)
                    currentIndex = (currentIndex + 1) % buttons.size
                    localHandler.postDelayed(this, 1000)
                }
            }
        }

        currentButtonRunnable = runnable
        localHandler.post(runnable)
    }


    fun startAnimation(
        dotsView: TextView,
        textView: TextView,
        progressBar: ProgressBar,
        baseText: String = "We are choosing one for you",
        finalText: String = "We’ve picked an animation for you!",
        dotDelay: Long = 300L,
        progressDelay: Long = 10L
    ) {
        Log.w("startAnimation", "startAnimation")
        stopAllAnimations()
        startDotAnimation(dotsView, dotDelay)
        startItemAnimation()
        startProgressAnimation(
            progressBar,
            dotsView,
            textView,
            finalText,
            progressDelay
        )
    }


    fun stopAllAnimations() {
        currentDotRunnable?.let { handlerDots?.removeCallbacks(it) }
        currentProgressRunnable?.let { handlerProgress?.removeCallbacks(it) }
        currentButtonRunnable?.let { handlerButtons?.removeCallbacks(it) }

        currentDotRunnable = null
        currentProgressRunnable = null
        currentButtonRunnable = null
    }


    private fun getRandomAnimationVideoUrl() {
        try {
            val jsonString = remoteConfigHelper.getString("animation_json")

            if (jsonString.isBlank()) {
                Log.w("AnimationSetup", "Remote config JSON is blank")
                return
            }

            val categories = Gson().fromJson(jsonString, Array<AnimationCategory>::class.java).toList()

            val validItems = categories
                .filter { it.name.isNotBlank() && it.content.isNotEmpty() }
                .flatMap { it.content }

            if (validItems.isEmpty()) {
                Log.w("AnimationSetup", "No valid animation items found")
                return
            }

            val itemRandom = validItems.random()
            mediaUrl = itemRandom.mediaOriginal

            Log.d("AnimationSetup mediaUrl", mediaUrl)

        } catch (e: Exception) {
            Log.e("AnimationSetup", "Error parsing animation data", e)
            null
        }
    }

    @OptIn(UnstableApi::class)
    private fun setupPlayer(binding: ItemSlideLayout4NewBinding, videoUrl: String) {
        try {

            player?.release()
            player = null
            player = ExoPlayer.Builder(binding.root.context).build().apply {

                addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        val stateString = when (playbackState) {
                            Player.STATE_IDLE -> "IDLE"
                            Player.STATE_BUFFERING -> "BUFFERING"
                            Player.STATE_READY -> {
                                isVideoRemoteReady = true
                                "READY"
                            }
                            Player.STATE_ENDED -> "ENDED"
                            else -> "UNKNOWN"
                        }
                        Log.d("VideoPlayer", "Playback state changed: $stateString")
                    }

                    override fun onPlayerError(error: PlaybackException) {
                        Log.e("VideoPlayer", "Player error: ${error.message}")
                    }
                })

                setMediaItem(MediaItem.fromUri(videoUrl))
                repeatMode = ExoPlayer.REPEAT_MODE_ONE
                playWhenReady = true
                prepare()
            }
            binding.playerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM

            binding.playerView.player = player
                binding.playerView.useController = false


        } catch (e: Exception) {
            Log.e("AnimationSetup", "Error setting up player", e)
        }
    }





    override fun setupListener() {
        super.setupListener()

        val layout1Binding = views[0] as ItemSlideLayout1NewBinding
        startingViewAdapter.setupNextButton(layout1Binding)

        layout1Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)

        }

        val layout2Binding = views[1] as ItemSlideLayout2Binding
        startingViewAdapter.setupNextButton(layout2Binding)

        layout2Binding.confirmAndContinueButton.setOnClickListener {
            appViewModel.acceptPrivacyPolicy()
            layout2Binding.confirmAndContinueButton.visibility = View.GONE
            layout2Binding.underText.text = getString(R.string.confirmed)
        }
        layout2Binding.privacyPolicyButton.setOnClickListener {
            startActivity(Intent(Intent.ACTION_VIEW, appViewModel.getPrivacyPolicyUrl().toUri()))
        }
        layout2Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)

        }

        val layout3Binding = views[2] as ItemSlideLayout3NewBinding
        startingViewAdapter.setupNextButton(layout3Binding)

        layout3Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout4Binding = views[3] as ItemSlideLayout4NewBinding

        layout4Binding.goToMain.setOnClickListener {
            startActivity(Intent(this, MainActivity::class.java))
            finish()

        }

        layout4Binding.acceptAnimation.setOnClickListener {
            acceptAnimation()
            if(OverlayPermissionUtils.isOverlayPermissionGranted(this)){
                navigationToMain()
            }
            else {
                binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
            }
        }
        val layout5Binding = views[4] as ItemSlideLayout5NewBinding

        layout5Binding.goToTutorial.setOnClickListener {
            startActivity(Intent(this, TutorialActivity::class.java))
        }
//        layout5Binding.goToSettings.setOnClickListener {
//            startActivity(Intent(this, TutorialActivity::class.java))
//        }
        layout5Binding.goToMain.setOnClickListener {
            navigationToMain()
        }

        layout5Binding.goToSettings.setOnClickListener {
                overlayPermission()
        }

//
//        val layout5Binding = views[4] as ItemSlideLayout5Binding
//        layout5Binding.switchInfo.setOnCheckedChangeListener { _, isChecked ->
//            if (isChecked) {
//                PermissionUtils.requestNotificationPermission(
//                        context = this,
//                        permissionLauncher = permissionLauncher,
//                        onPermissionGranted = {
//                            layout5Binding.switchInfo.isChecked = true
//                            layout5Binding.switchInfo.isEnabled = false
//                            appViewModel.setChargeAlarmEnabled(true)
//                        },
//                        onPermissionDenied = {
//                            layout5Binding.switchInfo.isChecked = false
//                            layout5Binding.switchInfo.isEnabled = true
//                            appViewModel.setChargeAlarmEnabled(false)
//                        }
//                )
//            }
//        }
//
//        layout5Binding.circularbar.setOnSeekBarChangeListener(
//                object : CircularSeekBar.OnCircularSeekBarChangeListener {
//                    override fun onProgressChanged(
//                            circularSeekBar: CircularSeekBar?,
//                            progress: Float,
//                            fromUser: Boolean
//                    ) {
//                        // Map 0-100 range to 60-100 range
//                        val mappedPercent = 60 + (progress * 0.4).toInt()
//                        layout5Binding.textPercent.text = buildString {
//                            append(mappedPercent)
//                            append("%")
//                        }
//                        appViewModel.setChargeAlarmPercent(mappedPercent)
//                    }
//
//                    override fun onStartTrackingTouch(seekBar: CircularSeekBar?) {}
//
//                    override fun onStopTrackingTouch(seekBar: CircularSeekBar?) {}
//                }
//        )
//        layout5Binding.nextPage.setOnClickListener {
//            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
//
//        }
//
//        val layout6Binding = views[5] as ItemSlideLayout6Binding
//        layout6Binding.dontkillmyappButton.setOnClickListener {
//            startActivity(Intent(Intent.ACTION_VIEW, appViewModel.getDoNotKillMyAppUrl().toUri()))
//        }
//        layout6Binding.workInBackgroundPermission.setOnClickListener {
//            requestBatteryOptimizationPermission()
//        }
//        layout6Binding.nextPage.setOnClickListener {
//            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
//
//        }
//        val layout7Binding = views[6] as ItemSlideLayout7Binding
//        layout7Binding.nextPage.setOnClickListener {
//            // show IV
//            loadIV()
//
//            appViewModel.setShowedStartPage(true)
//            startActivity(Intent(this, MainActivity::class.java))
//            finish()
//
//        }
//
//        layout7Binding.changeCapacity.setOnClickListener {
//            applovinInterstitialAdManager.showInterstitialAd(
//                "default_iv",
//                this@StartingActivity
//            ) {
//                ChangeCapacityDialog(this, batteryViewModel).show()
//            }
//        }

        binding.slidePager.clearOnPageChangeListeners()
        binding.slidePager.addOnPageChangeListener(
                object : androidx.viewpager.widget.ViewPager.OnPageChangeListener {
                    override fun onPageScrolled(
                            position: Int,
                            positionOffset: Float,
                            positionOffsetPixels: Int
                    ) {}

                    override fun onPageSelected(position: Int) {
                        if (views[position] is ItemSlideLayout2Binding && position > 1 && !appViewModel.isPrivacyPolicyAccepted()) {
                            binding.slidePager.setCurrentItem(1, true)
                            startingViewAdapter.setupNextButton(binding)

                        }
                        if (views[position + 1] is ItemSlideLayout3NewBinding) {
                            Log.d("ItemSlideLayout3NewBinding", "$position")

                            val layout = views[2] as ItemSlideLayout3NewBinding
                            startAnimation(
                                layout.animatedDots,
                                layout.selectingText,
                                layout.progressBar
                            )
                            val view = startingViewAdapter.getViewAt(position)

                            if (position >= 1) {
                                applovinInterstitialAdManager.loadInterstitialAd()
                            }
                            view?.let {
                                startingViewAdapter.setupNextButton(it)
                            }
                        }
                        // Điều kiện page 4
                        if (views[position + 1] is ItemSlideLayout4NewBinding) {
                            if (!isVideoRemoteReady) {
                                Log.d("VideoPlayer", "Remote video not ready -> use local")

                                playLocalFallbackVideo(layout4Binding)
                            } else if (player?.isPlaying == false) {
                                player?.playWhenReady = true
                                player?.play()
                            }
                        } else {
                            if (player?.isPlaying == true)
                                player?.pause()
                        }
                    }
                    override fun onPageScrollStateChanged(state: Int) {}
                }
        )

    }


    @OptIn(UnstableApi::class)
    private val localVideoUri: String
        get() = "android.resource://${this.packageName}/${R.raw.local_video_animation}"


    private fun playLocalFallbackVideo(binding: ItemSlideLayout4NewBinding) {
        mediaUrl = localVideoUri
        player?.release()
        player = null
        player = ExoPlayer.Builder(binding.root.context).build().apply {
            setMediaItem(MediaItem.fromUri(localVideoUri))
            repeatMode = ExoPlayer.REPEAT_MODE_ONE
            playWhenReady = true
            prepare()
        }
        binding.playerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM
        binding.playerView.player = player
        binding.playerView.useController = false
    }


    private fun acceptAnimation() {
        animationViewModel.applyAnimation(mediaUrl)
        proceedNotOverlayPermission()
    }

    private fun overlayPermission() {
        if (!OverlayPermissionUtils.isOverlayPermissionGranted(this)) {
            val intent = OverlayPermissionUtils.createOverlayPermissionIntent(this)
            permissionLauncher!!.launch(intent)
        }
    }


    private fun proceedNotOverlayPermission() {
//        val loadingDialog =
//            com.tqhit.battery.one.dialog.utils.LoadingDialog(this, getString(R.string.saving_video))
//        loadingDialog.show()

        // 1. Extract the dynamic filename from the URL
        val dynamicFileName = VideoUtils.getFileNameFromUrl(mediaUrl)
        val destinationFile = File(filesDir, dynamicFileName)

        // --- INSTANT ACTION ---
        // 2. Save the FULL PATH with the DYNAMIC filename for permanent storage
        appRepository.setVideoPath(destinationFile.absolutePath)
        // Also save the URL for immediate playback fallback
        appRepository.setAppliedAnimationUrl(mediaUrl)
        appRepository.setAnimationOverlayEnabled(true)
        animationViewModel.setApplied(mediaUrl)

        if (mediaUrl == null) {
//            if (loadingDialog.isShowing && !isFinishing && !isDestroyed) {
//                loadingDialog.dismiss()
//            }
            if (!isFinishing && !isDestroyed) {
                Toast.makeText(this, getString(R.string.no_video_url_provided), Toast.LENGTH_SHORT)
                    .show()
            }
            return
        }
        lifecycleScope.launch {
            try {
                BatteryLogger.d("ProceedDebug", "Start downloading: $mediaUrl")

                // Use enhanced video utils for better performance with preloaded files
                val destFile = videoUtils.downloadAndSaveVideoEnhanced(mediaUrl,dynamicFileName)

                BatteryLogger.d("ProceedDebug", "Downloaded to: ${destFile.absolutePath}")

                appRepository.setVideoPath(destFile.absolutePath)

                BatteryLogger.d("ProceedDebug", "Video path saved")

//                if (loadingDialog.isShowing && !isFinishing && !isDestroyed) {
//                    loadingDialog.dismiss()
//                }
                if (!isFinishing && !isDestroyed) {
                    Toast.makeText(
                        this@StartingActivity,
                        getString(R.string.video_saved),
                        Toast.LENGTH_SHORT
                    ).show()
//                    val intent =
//                        Intent(this@StartingActivity, TutorialActivity::class.java)
//                    startActivity(intent)
//                    finish()

                } else {
                    finish()
                }
                animationViewModel.setApplied(mediaUrl)
            } catch (e: Exception) {
//                if (loadingDialog.isShowing && !isFinishing && !isDestroyed) {
//                    loadingDialog.dismiss()
//                }
                if (!isFinishing && !isDestroyed) {
                    Toast.makeText(
                        this@StartingActivity,
                        getString(R.string.error_accessing_file),
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }
    }

//    private fun requestBatteryOptimizationPermission() {
//        try {
//            if (batteryViewModel.isIgnoringBatteryOptimizations()) {
//                Toast.makeText(this, R.string.permission_granted, Toast.LENGTH_SHORT).show()
//                val layout6Binding = views[5] as ItemSlideLayout6Binding
//                layout6Binding.workInBackgroundPermission.isEnabled = false
//                layout6Binding.workInBackgroundPermission.text =
//                        getString(R.string.permission_granted)
//                return
//            }
//
//            val intent =
//                    Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
//                        data = "package:$packageName".toUri()
//                    }
//            startActivity(intent)
//        } catch (e: Exception) {
//            Toast.makeText(this, R.string.unexpected_error, Toast.LENGTH_SHORT).show()
//        }
//    }

    override fun onPause() {
        super.onPause()
        if (player?.isPlaying == true)
            player?.pause()
    }

    override fun onResume() {
        super.onResume()
        if (views.size > binding.slidePager.currentItem && views[binding.slidePager.currentItem+1] is ItemSlideLayout4NewBinding) {
            if (player?.isPlaying == false) {
                player?.playWhenReady = true
                player?.play()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        player?.release()
        player = null
    }
}
